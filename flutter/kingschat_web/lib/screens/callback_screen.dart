import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:web/web.dart' as web;
import '../services/auth_service.dart';

class CallbackScreen extends StatefulWidget {
  const CallbackScreen({super.key});

  @override
  State<CallbackScreen> createState() => _CallbackScreenState();
}

class _CallbackScreenState extends State<CallbackScreen> {
  bool _isProcessing = true;
  String _status = 'Processing authentication...';

  @override
  void initState() {
    super.initState();
    _handleCallback();
  }

  Future<void> _handleCallback() async {
    try {
      setState(() {
        _status = 'Authenticating with KingsChat...';
      });

      // Parse tokens from URL fragment or query parameters
      final uri = Uri.parse(web.window.location.href);
      debugPrint('Callback URL: ${uri.toString()}');
      debugPrint('URL fragment: ${uri.fragment}');
      debugPrint('URL query parameters: ${uri.queryParameters}');

      String? accessToken;
      String? refreshToken;

      // Check URL fragment first (OAuth implicit flow)
      if (uri.fragment.isNotEmpty) {
        final fragmentParams = Uri.splitQueryString(uri.fragment);
        debugPrint('Fragment parameters: $fragmentParams');
        accessToken = fragmentParams['access_token'];
        refreshToken = fragmentParams['refresh_token'];
      }

      // Check query parameters (OAuth authorization code flow)
      if (accessToken == null) {
        accessToken = uri.queryParameters['access_token'] ?? uri.queryParameters['accessToken'];
        refreshToken = uri.queryParameters['refresh_token'] ?? uri.queryParameters['refreshToken'];
      }

      debugPrint('Extracted access token: ${accessToken != null ? (accessToken.length > 20 ? '${accessToken.substring(0, 20)}...' : accessToken) : 'null'}');
      debugPrint('Extracted refresh token: ${refreshToken != null ? (refreshToken.length > 20 ? '${refreshToken.substring(0, 20)}...' : refreshToken) : 'null'}');

      if (accessToken == null) {
        throw Exception('No access token found in callback URL');
      }

      setState(() {
        _status = 'Sending tokens to backend...';
      });

      // Send tokens to PHP backend
      await _sendTokensToBackend(accessToken, refreshToken);

      // Handle the callback with the auth service
      final authService = Provider.of<AuthService>(context, listen: false);
      final success = await authService.handleCallback(accessToken, refreshToken);

      if (success) {
        setState(() {
          _status = 'Authentication successful! Redirecting...';
        });

        // Navigate to dashboard using GoRouter
        if (mounted) {
          context.go('/dashboard');
        }
      } else {
        setState(() {
          _status = 'Authentication failed. Please try again.';
          _isProcessing = false;
        });
      }
    } catch (e) {
      setState(() {
        _status = 'Error processing authentication: $e';
        _isProcessing = false;
      });
    }
  }

  Future<void> _sendTokensToBackend(String accessToken, String? refreshToken) async {
    try {
      // Send tokens to the PHP callback.php endpoint
      final response = await http.post(
        Uri.parse('http://localhost/kingschat/callback.php'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'accessToken': accessToken,
          'refreshToken': refreshToken,
        }),
      );

      debugPrint('Backend response status: ${response.statusCode}');
      debugPrint('Backend response body: ${response.body}');

      if (response.statusCode != 200) {
        throw Exception('Failed to send tokens to backend: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error sending tokens to backend: $e');
      // Don't throw here, as we still want to proceed with Flutter auth
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF4A90E2),
              Color(0xFF357ABD),
            ],
          ),
        ),
        child: Center(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 400),
            margin: const EdgeInsets.all(24),
            child: Card(
              elevation: 8,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Loading indicator or status icon
                    if (_isProcessing)
                      const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4A90E2)),
                      )
                    else
                      Icon(
                        _status.contains('successful') 
                            ? Icons.check_circle_outline
                            : Icons.error_outline,
                        size: 64,
                        color: _status.contains('successful') 
                            ? Colors.green 
                            : Colors.red,
                      ),
                    
                    const SizedBox(height: 24),
                    
                    // Status text
                    Text(
                      _status,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2C3E50),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Action button (only show if not processing and not successful)
                    if (!_isProcessing && !_status.contains('successful'))
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pushReplacementNamed('/');
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF4A90E2),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text(
                            'Back to Login',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
